# 🚀 一鍵啟動說明

## Windows 用戶

### 方法一：直接執行
1. 雙擊 `啟動系統.bat` 檔案
2. 系統會自動：
   - 檢查 Python 環境
   - 建立虛擬環境（第一次使用）
   - 安裝必要套件（第一次使用）
   - 顯示電腦 IP 位址
   - 啟動系統

### 方法二：建立桌面快捷方式
1. 右鍵點擊 `啟動系統.bat`
2. 選擇「建立捷徑」
3. 將捷徑拖到桌面
4. 重新命名為「小學作業掃描系統」
5. 以後直接雙擊桌面圖示即可啟動

## macOS/Linux 用戶

### 方法一：終端機執行
1. 開啟終端機
2. 切換到專案資料夾：`cd qr-mobile`
3. 執行：`./啟動系統.sh`

### 方法二：Finder 直接執行（macOS）
1. 在 Finder 中找到 `啟動系統.sh`
2. 右鍵點擊 → 「開啟方式」→ 「終端機」
3. 系統會自動啟動

### 方法三：建立 Dock 快捷方式（macOS）
1. 開啟「自動操作」(Automator)
2. 選擇「應用程式」
3. 加入「執行 Shell 指令碼」動作
4. 輸入：
   ```bash
   cd "$(dirname "$0")/qr-mobile"
   ./啟動系統.sh
   ```
5. 儲存為「小學作業掃描系統.app」
6. 拖到 Dock 中

## 🎯 啟動後的步驟

1. **等待系統啟動**
   - 看到「Running on http://0.0.0.0:5000」表示成功
   - 記下顯示的電腦 IP 位址

2. **手機連線**
   - 確保手機和電腦在同一個 Wi-Fi
   - 手機瀏覽器開啟：`http://[電腦IP]:5000`
   - 允許相機權限

3. **開始使用**
   - 點擊「開始掃描」
   - 掃描 QR 碼或手動輸入
   - 查看統計資料

## 🔧 常見問題

### Q: 找不到 Python？
**A:** 請先安裝 Python 3.7 或更新版本
- Windows: https://www.python.org/downloads/
- macOS: `brew install python3`
- Ubuntu: `sudo apt install python3 python3-pip python3-venv`

### Q: 手機連不上？
**A:** 檢查以下項目：
1. 手機和電腦是否在同一個 Wi-Fi
2. 電腦防火牆是否阻擋 5000 埠
3. IP 位址是否正確

### Q: 系統啟動失敗？
**A:** 請檢查：
1. 是否在 qr-mobile 資料夾中執行
2. 網路連線是否正常
3. 磁碟空間是否足夠

### Q: 想要停止系統？
**A:** 在命令視窗按 `Ctrl+C` 即可停止

## 💡 使用技巧

1. **第一次啟動會比較慢**（需要安裝套件）
2. **之後啟動會很快**（環境已建立）
3. **可以建立桌面快捷方式**方便使用
4. **系統會自動顯示 IP 位址**，直接複製給手機使用
5. **支援多台手機同時連線**掃描

## 🎉 享受便利的作業管理！

一鍵啟動後，老師就可以輕鬆管理學生作業，再也不用擔心漏掉任何一個學生的作業了！
