<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系統測試頁面</title>
    <style>
        body {
            font-family: system-ui, -apple-system, "Segoe UI", <PERSON><PERSON>, "Noto Sans TC", sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #e3f2fd;
            border-radius: 8px;
            background: #fafafa;
        }
        .test-section h2 {
            color: #1976d2;
            margin-top: 0;
        }
        button {
            background: #1976d2;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            width: 100%;
        }
        button:hover {
            background: #1565c0;
        }
        .success {
            background: #28a745;
        }
        .success:hover {
            background: #218838;
        }
        .danger {
            background: #dc3545;
        }
        .danger:hover {
            background: #c82333;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .result.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .result.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .qr-display {
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            background: white;
            border: 2px dashed #ddd;
            border-radius: 8px;
        }
        .qr-content {
            font-family: monospace;
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 系統測試頁面</h1>
        
        <!-- 連線測試 -->
        <div class="test-section">
            <h2>🌐 連線測試</h2>
            <p>測試與後端系統的連線狀況</p>
            <button onclick="testConnection()">測試連線</button>
            <div id="connectionResult"></div>
        </div>

        <!-- 手動輸入測試 -->
        <div class="test-section">
            <h2>✏️ 手動輸入測試</h2>
            <p>測試後端 API 是否正常工作</p>
            <input type="text" id="manualInput" placeholder="輸入測試內容" value="HW|1-05|數學習作P25">
            <button onclick="testManualInput()">測試手動輸入</button>
            <div id="manualResult"></div>
        </div>

        <!-- QR 碼測試 -->
        <div class="test-section">
            <h2>📱 QR 碼測試</h2>
            <p>產生測試用的 QR 碼內容，用其他 APP 掃描後貼到手動輸入測試</p>
            
            <div class="qr-display">
                <h3>測試 QR 碼內容：</h3>
                <div class="qr-content" id="testQRContent">HW|1-05|數學習作P25</div>
                <p><small>請用手機的 QR 碼產生器 APP 產生此內容的 QR 碼</small></p>
            </div>
            
            <button onclick="generateTestQR('student')">產生學生座號測試</button>
            <button onclick="generateTestQR('assignment')">產生作業名稱測試</button>
            <button onclick="generateTestQR('full')">產生完整格式測試</button>
            <button onclick="copyQRContent()">複製 QR 碼內容</button>
        </div>

        <!-- 統計查詢測試 -->
        <div class="test-section">
            <h2>📊 統計查詢測試</h2>
            <p>測試各種統計功能是否正常</p>
            <button onclick="testStats('submissions_today')">測試今日繳交</button>
            <button onclick="testStats('missing_submissions')">測試未繳交清單</button>
            <button onclick="testStats('class_summary')">測試班級統計</button>
            <div id="statsResult"></div>
        </div>

        <!-- 系統資訊 -->
        <div class="test-section">
            <h2>ℹ️ 系統資訊</h2>
            <div id="systemInfo" class="result info">
正在載入系統資訊...
            </div>
            <button onclick="getSystemInfo()">重新載入系統資訊</button>
        </div>
    </div>

    <script>
        // 系統基本資訊
        function getSystemInfo() {
            const info = `
系統時間：${new Date().toLocaleString()}
當前網址：${window.location.href}
用戶代理：${navigator.userAgent}
螢幕解析度：${screen.width} x ${screen.height}
視窗大小：${window.innerWidth} x ${window.innerHeight}
語言設定：${navigator.language}
線上狀態：${navigator.onLine ? '線上' : '離線'}
            `;
            document.getElementById('systemInfo').textContent = info;
        }

        // 測試連線
        async function testConnection() {
            const resultDiv = document.getElementById('connectionResult');
            resultDiv.innerHTML = '<div class="result info">正在測試連線...</div>';
            
            try {
                const response = await fetch('/api/stats/submissions_today');
                if (response.ok) {
                    resultDiv.innerHTML = '<div class="result success">✅ 連線成功！後端系統正常運行</div>';
                } else {
                    resultDiv.innerHTML = `<div class="result error">❌ 連線失敗：HTTP ${response.status}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">❌ 連線錯誤：${error.message}</div>`;
            }
        }

        // 測試手動輸入
        async function testManualInput() {
            const input = document.getElementById('manualInput').value;
            const resultDiv = document.getElementById('manualResult');
            
            if (!input) {
                resultDiv.innerHTML = '<div class="result error">請輸入測試內容</div>';
                return;
            }
            
            resultDiv.innerHTML = '<div class="result info">正在測試...</div>';
            
            try {
                const response = await fetch('/api/scan', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ data: input })
                });
                
                const result = await response.json();
                
                if (response.ok && result.ok) {
                    resultDiv.innerHTML = `<div class="result success">✅ 測試成功！\n${result.message}</div>`;
                } else {
                    resultDiv.innerHTML = `<div class="result error">❌ 測試失敗：\n${result.message || '未知錯誤'}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">❌ 請求錯誤：${error.message}</div>`;
            }
        }

        // 產生測試 QR 碼內容
        function generateTestQR(type) {
            let content = '';
            const now = new Date();
            const randomNum = Math.floor(Math.random() * 30) + 1;
            
            switch(type) {
                case 'student':
                    content = `1-${randomNum.toString().padStart(2, '0')}`;
                    break;
                case 'assignment':
                    content = `測試作業${now.getHours()}${now.getMinutes()}`;
                    break;
                case 'full':
                    content = `HW|1-${randomNum.toString().padStart(2, '0')}|測試作業${now.getHours()}${now.getMinutes()}`;
                    break;
            }
            
            document.getElementById('testQRContent').textContent = content;
        }

        // 複製 QR 碼內容
        function copyQRContent() {
            const content = document.getElementById('testQRContent').textContent;
            navigator.clipboard.writeText(content).then(() => {
                alert('已複製到剪貼簿：' + content);
            }).catch(() => {
                // 備用複製方法
                const textArea = document.createElement('textarea');
                textArea.value = content;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('已複製到剪貼簿：' + content);
            });
        }

        // 測試統計功能
        async function testStats(endpoint) {
            const resultDiv = document.getElementById('statsResult');
            resultDiv.innerHTML = '<div class="result info">正在查詢統計資料...</div>';
            
            try {
                const response = await fetch(`/api/stats/${endpoint}`);
                const data = await response.json();
                
                if (response.ok) {
                    const formatted = JSON.stringify(data, null, 2);
                    resultDiv.innerHTML = `<div class="result success">✅ 統計查詢成功：\n${formatted}</div>`;
                } else {
                    resultDiv.innerHTML = `<div class="result error">❌ 統計查詢失敗：HTTP ${response.status}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">❌ 查詢錯誤：${error.message}</div>`;
            }
        }

        // 頁面載入時執行
        window.onload = function() {
            getSystemInfo();
            // 自動測試連線
            setTimeout(testConnection, 1000);
        };
    </script>
</body>
</html>
