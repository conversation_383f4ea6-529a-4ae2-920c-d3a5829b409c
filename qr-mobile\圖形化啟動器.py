#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小學作業掃描系統 - 圖形化啟動器
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import subprocess
import threading
import socket
import sys
import os
import webbrowser

class SystemLauncher:
    def __init__(self, root):
        self.root = root
        self.root.title("📚 小學作業掃描系統")
        self.root.geometry("600x500")
        self.root.resizable(False, False)
        
        # 設定圖示（如果有的話）
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass
        
        self.process = None
        self.setup_ui()
        self.get_ip_address()
    
    def setup_ui(self):
        # 主標題
        title_frame = tk.Frame(self.root, bg="#2c3e50", height=80)
        title_frame.pack(fill="x")
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, text="📚 小學作業掃描系統", 
                              font=("Microsoft JhengHei", 18, "bold"),
                              fg="white", bg="#2c3e50")
        title_label.pack(expand=True)
        
        # 主要內容區域
        main_frame = tk.Frame(self.root, padx=20, pady=20)
        main_frame.pack(fill="both", expand=True)
        
        # 系統狀態
        status_frame = tk.LabelFrame(main_frame, text="🌐 系統狀態", font=("Microsoft JhengHei", 12))
        status_frame.pack(fill="x", pady=(0, 15))
        
        self.status_label = tk.Label(status_frame, text="系統未啟動", 
                                   font=("Microsoft JhengHei", 11), fg="red")
        self.status_label.pack(pady=10)
        
        self.ip_label = tk.Label(status_frame, text="正在取得 IP 位址...", 
                               font=("Microsoft JhengHei", 10))
        self.ip_label.pack(pady=5)
        
        # 控制按鈕
        button_frame = tk.Frame(main_frame)
        button_frame.pack(fill="x", pady=(0, 15))
        
        self.start_button = tk.Button(button_frame, text="🚀 啟動系統", 
                                    command=self.start_system,
                                    font=("Microsoft JhengHei", 12, "bold"),
                                    bg="#27ae60", fg="white", height=2)
        self.start_button.pack(side="left", fill="x", expand=True, padx=(0, 5))
        
        self.stop_button = tk.Button(button_frame, text="🛑 停止系統", 
                                   command=self.stop_system,
                                   font=("Microsoft JhengHei", 12, "bold"),
                                   bg="#e74c3c", fg="white", height=2, state="disabled")
        self.stop_button.pack(side="left", fill="x", expand=True, padx=(5, 0))
        
        # 快速連結
        link_frame = tk.LabelFrame(main_frame, text="🔗 快速連結", font=("Microsoft JhengHei", 12))
        link_frame.pack(fill="x", pady=(0, 15))
        
        link_buttons = [
            ("📱 開啟手機頁面", self.open_mobile_page),
            ("🏷️ QR 碼產生器", self.open_qr_generator),
            ("📖 使用說明", self.open_guide)
        ]
        
        for text, command in link_buttons:
            btn = tk.Button(link_frame, text=text, command=command,
                          font=("Microsoft JhengHei", 10), width=20)
            btn.pack(side="left", padx=5, pady=10, expand=True, fill="x")
        
        # 系統日誌
        log_frame = tk.LabelFrame(main_frame, text="📋 系統日誌", font=("Microsoft JhengHei", 12))
        log_frame.pack(fill="both", expand=True)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=8, 
                                                font=("Consolas", 9))
        self.log_text.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 初始日誌
        self.log("歡迎使用小學作業掃描系統！")
        self.log("請點擊「啟動系統」開始使用")
    
    def get_ip_address(self):
        """取得本機 IP 位址"""
        try:
            # 連接到外部地址來取得本機 IP
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            ip = s.getsockname()[0]
            s.close()
            self.ip_address = ip
            self.ip_label.config(text=f"電腦 IP 位址：{ip}\n手機請開啟：http://{ip}:5000")
        except:
            self.ip_address = "無法取得"
            self.ip_label.config(text="無法取得 IP 位址，請檢查網路連線")
    
    def log(self, message):
        """新增日誌訊息"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.root.update()
    
    def start_system(self):
        """啟動系統"""
        if not os.path.exists("app.py"):
            messagebox.showerror("錯誤", "找不到 app.py 檔案\n請確認您在正確的資料夾中執行此程式")
            return
        
        self.log("正在啟動系統...")
        self.start_button.config(state="disabled")
        
        # 在背景執行系統啟動
        threading.Thread(target=self._start_system_thread, daemon=True).start()
    
    def _start_system_thread(self):
        """在背景執行系統啟動"""
        try:
            # 檢查虛擬環境
            if not os.path.exists(".venv"):
                self.log("建立虛擬環境...")
                subprocess.run([sys.executable, "-m", "venv", ".venv"], check=True)
            
            # 取得 Python 執行檔路徑
            if os.name == 'nt':  # Windows
                python_exe = os.path.join(".venv", "Scripts", "python.exe")
                pip_exe = os.path.join(".venv", "Scripts", "pip.exe")
            else:  # macOS/Linux
                python_exe = os.path.join(".venv", "bin", "python")
                pip_exe = os.path.join(".venv", "bin", "pip")
            
            # 安裝套件
            self.log("檢查並安裝必要套件...")
            subprocess.run([pip_exe, "install", "-r", "requirements.txt"], 
                         check=True, capture_output=True)
            
            # 啟動 Flask 應用
            self.log("啟動 Flask 應用...")
            self.process = subprocess.Popen([python_exe, "app.py"],
                                          stdout=subprocess.PIPE,
                                          stderr=subprocess.STDOUT,
                                          universal_newlines=True,
                                          bufsize=1)
            
            # 更新 UI
            self.root.after(0, self._system_started)
            
            # 讀取輸出
            for line in self.process.stdout:
                self.root.after(0, lambda l=line: self.log(l.strip()))
                
        except subprocess.CalledProcessError as e:
            self.root.after(0, lambda: self.log(f"啟動失敗：{e}"))
            self.root.after(0, lambda: self.start_button.config(state="normal"))
        except Exception as e:
            self.root.after(0, lambda: self.log(f"發生錯誤：{e}"))
            self.root.after(0, lambda: self.start_button.config(state="normal"))
    
    def _system_started(self):
        """系統啟動完成後的 UI 更新"""
        self.status_label.config(text="系統運行中", fg="green")
        self.stop_button.config(state="normal")
        self.log("✅ 系統啟動成功！")
        self.log(f"📱 手機請開啟：http://{self.ip_address}:5000")
    
    def stop_system(self):
        """停止系統"""
        if self.process:
            self.log("正在停止系統...")
            self.process.terminate()
            self.process = None
            self.status_label.config(text="系統已停止", fg="red")
            self.start_button.config(state="normal")
            self.stop_button.config(state="disabled")
            self.log("🛑 系統已停止")
    
    def open_mobile_page(self):
        """開啟手機頁面"""
        if self.ip_address != "無法取得":
            webbrowser.open(f"http://{self.ip_address}:5000")
        else:
            messagebox.showwarning("警告", "無法取得 IP 位址")
    
    def open_qr_generator(self):
        """開啟 QR 碼產生器"""
        if os.path.exists("QR碼產生器.html"):
            webbrowser.open(f"file://{os.path.abspath('QR碼產生器.html')}")
        else:
            messagebox.showwarning("警告", "找不到 QR碼產生器.html 檔案")
    
    def open_guide(self):
        """開啟使用說明"""
        if os.path.exists("小學使用指南.md"):
            webbrowser.open(f"file://{os.path.abspath('小學使用指南.md')}")
        else:
            messagebox.showwarning("警告", "找不到使用說明檔案")
    
    def on_closing(self):
        """關閉程式時的處理"""
        if self.process:
            self.stop_system()
        self.root.destroy()

if __name__ == "__main__":
    root = tk.Tk()
    app = SystemLauncher(root)
    root.protocol("WM_DELETE_WINDOW", app.on_closing)
    root.mainloop()
