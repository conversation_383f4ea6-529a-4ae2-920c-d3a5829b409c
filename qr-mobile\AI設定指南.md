# 🤖 AI 智能 QR 碼產生器設定指南

## 🌟 功能概述

AI 智能 QR 碼產生器讓您可以用自然語言描述需求，AI 會自動生成對應的 QR 碼內容。

### ✨ 主要特色
- **自然語言輸入**：用平常說話的方式描述需求
- **智能格式識別**：AI 自動判斷並生成正確格式
- **多種 AI 支援**：支援 OpenAI、Claude、本地模型
- **備用機制**：即使沒有 AI API 也能正常工作
- **即時生成**：生成 QR 碼圖片和內容

## 🚀 快速開始

### 基本使用（無需設定）
1. 開啟 `AI智能QR產生器.html`
2. 在文字框輸入需求，例如：
   - "幫我生成1年級5號學生的數學習作第25頁作業繳交QR碼"
   - "生成2年級12號學生座號QR碼"
   - "製作國語作業簿的QR碼"
3. 點擊「AI 生成 QR 碼」
4. 系統會使用內建規則引擎生成 QR 碼

## 🔧 進階 AI 設定

### 方案 A：OpenAI API
1. **取得 API 金鑰**
   - 前往 https://platform.openai.com/
   - 註冊帳號並取得 API Key

2. **設定環境變數**
   ```bash
   # Windows
   set OPENAI_API_KEY=your_api_key_here
   
   # macOS/Linux
   export OPENAI_API_KEY=your_api_key_here
   ```

3. **重新啟動系統**
   - 停止當前系統（Ctrl+C）
   - 重新執行 `python app.py`

### 方案 B：Claude API
1. **取得 API 金鑰**
   - 前往 https://console.anthropic.com/
   - 註冊帳號並取得 API Key

2. **設定環境變數**
   ```bash
   # Windows
   set CLAUDE_API_KEY=your_api_key_here
   
   # macOS/Linux
   export CLAUDE_API_KEY=your_api_key_here
   ```

### 方案 C：本地 AI 模型 (Ollama)
1. **安裝 Ollama**
   - 前往 https://ollama.ai/
   - 下載並安裝 Ollama

2. **下載模型**
   ```bash
   ollama pull llama2
   # 或其他模型：ollama pull codellama
   ```

3. **啟動 Ollama 服務**
   ```bash
   ollama serve
   ```

4. **設定環境變數（可選）**
   ```bash
   # 如果 Ollama 不在預設位置
   export LOCAL_AI_URL=http://localhost:11434
   ```

## 📝 AI 提示範例

### 學生座號
```
生成1年級5號學生的座號QR碼
製作2年級12號的學生標籤
幫我做3年級8號學生的QR碼
```

### 作業名稱
```
製作數學習作第25頁的QR碼
生成國語作業簿QR碼
英語練習本的作業標籤
```

### 完整作業繳交
```
幫我生成1年級5號學生的數學習作第25頁作業繳交QR碼
2年級12號學生要繳交國語作業簿
3年級全班的英語練習本繳交
```

### 庫存盤點
```
盤點5支2B鉛筆的庫存QR碼
製作3個橡皮擦的盤點標籤
庫存管理：10把尺子
```

## 🔍 AI 工作原理

### 1. 智能解析流程
```
用戶輸入 → AI 分析 → 格式識別 → 內容生成 → QR 碼產生
```

### 2. 多層備用機制
```
OpenAI API → Claude API → 本地模型 → 規則引擎
```

### 3. 格式標準化
- **學生座號**：`1-05`
- **作業名稱**：`數學習作P25`
- **完整繳交**：`HW|1-05|數學習作P25`
- **庫存盤點**：`INV|2B鉛筆|5`

## ⚙️ 系統配置

### 環境變數設定
```bash
# OpenAI 設定
OPENAI_API_KEY=sk-...

# Claude 設定  
CLAUDE_API_KEY=sk-ant-...

# 本地 AI 設定
LOCAL_AI_URL=http://localhost:11434
```

### 模型選擇建議
- **OpenAI**：`gpt-3.5-turbo`（經濟實惠）或 `gpt-4`（更準確）
- **Claude**：`claude-3-haiku-20240307`（快速）或 `claude-3-sonnet-20240229`（平衡）
- **本地模型**：`llama2`（通用）或 `codellama`（程式理解更好）

## 🐛 故障排除

### 問題 1：AI API 無回應
**解決方案**：
1. 檢查 API 金鑰是否正確
2. 確認網路連線正常
3. 檢查 API 額度是否用完
4. 系統會自動降級到規則引擎

### 問題 2：本地模型無法連接
**解決方案**：
1. 確認 Ollama 服務正在運行
2. 檢查埠號設定（預設 11434）
3. 確認模型已下載：`ollama list`

### 問題 3：生成結果不準確
**解決方案**：
1. 提供更詳細的描述
2. 使用類型提示功能
3. 嘗試不同的 AI 模型
4. 手動調整生成結果

## 💡 使用技巧

### 1. 提示詞優化
- **具體明確**：包含班級、座號、作業名稱等詳細資訊
- **使用關鍵詞**：學生、作業、繳交、盤點等
- **格式提示**：可以說明想要的格式類型

### 2. 批量生成
```
生成1年級全班30個學生的數學習作P25繳交QR碼
製作2年級1-15號學生的座號標籤
```

### 3. 自訂格式
```
我要一個格式為 HW|3-08|英語練習本 的QR碼
請生成 INV|彩色筆|12 格式的庫存標籤
```

## 🔒 隱私與安全

### 資料處理
- 所有 AI 請求都經過加密傳輸
- 不會儲存用戶的 API 金鑰
- 生成的內容僅用於 QR 碼製作

### 本地優先
- 優先使用本地模型保護隱私
- 支援完全離線的規則引擎
- 可選擇不使用雲端 AI 服務

## 📈 效能優化

### 回應速度排序
1. **規則引擎**：< 100ms
2. **本地模型**：1-3 秒
3. **Claude API**：2-5 秒
4. **OpenAI API**：3-8 秒

### 成本考量
- **規則引擎**：免費
- **本地模型**：免費（需要硬體資源）
- **Claude API**：按使用量計費
- **OpenAI API**：按使用量計費

現在您可以享受 AI 驅動的智能 QR 碼生成體驗了！
