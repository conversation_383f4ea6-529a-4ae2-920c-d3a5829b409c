"""
AI 大模型整合配置
支援多種 AI 服務：OpenAI、<PERSON>、本地模型等
"""

import requests
import json
import os
from typing import Optional, Dict, Any

class AIQRGenerator:
    def __init__(self):
        # AI 服務配置
        self.openai_api_key = os.getenv('OPENAI_API_KEY', '')
        self.claude_api_key = os.getenv('CLAUDE_API_KEY', '')
        self.local_api_url = os.getenv('LOCAL_AI_URL', 'http://localhost:11434')  # Ollama
        
        # 系統提示詞
        self.system_prompt = """
你是一個專門為小學老師設計的 QR 碼內容生成助手。
請根據用戶的自然語言描述，生成對應的 QR 碼內容。

QR 碼格式規範：
1. 學生座號：格式為 "班級-座號"，例如 "1-05"（1年級5號）
2. 作業名稱：直接使用作業名稱，例如 "數學習作P25"
3. 完整作業繳交：格式為 "HW|學生座號|作業名稱"，例如 "HW|1-05|數學習作P25"
4. 庫存盤點：格式為 "INV|物品代碼|數量"，例如 "INV|2B鉛筆|5"

請分析用戶的需求，只返回對應的 QR 碼內容，不要包含其他解釋。
如果無法確定格式，請返回最可能的內容。
"""

    def generate_qr_content(self, user_prompt: str, qr_type: str = "auto") -> Optional[str]:
        """
        使用 AI 生成 QR 碼內容
        
        Args:
            user_prompt: 用戶的自然語言描述
            qr_type: QR 碼類型提示
            
        Returns:
            生成的 QR 碼內容，失敗時返回 None
        """
        
        # 嘗試不同的 AI 服務
        content = None
        
        # 1. 嘗試 OpenAI
        if self.openai_api_key and not content:
            content = self._try_openai(user_prompt, qr_type)
        
        # 2. 嘗試 Claude
        if self.claude_api_key and not content:
            content = self._try_claude(user_prompt, qr_type)
        
        # 3. 嘗試本地模型 (Ollama)
        if not content:
            content = self._try_local_ai(user_prompt, qr_type)
        
        # 4. 備用：使用規則引擎
        if not content:
            content = self._fallback_rule_engine(user_prompt, qr_type)
        
        return content

    def _try_openai(self, user_prompt: str, qr_type: str) -> Optional[str]:
        """嘗試使用 OpenAI API"""
        try:
            headers = {
                'Authorization': f'Bearer {self.openai_api_key}',
                'Content-Type': 'application/json'
            }
            
            data = {
                'model': 'gpt-3.5-turbo',
                'messages': [
                    {'role': 'system', 'content': self.system_prompt},
                    {'role': 'user', 'content': f"類型提示：{qr_type}\n用戶需求：{user_prompt}"}
                ],
                'max_tokens': 100,
                'temperature': 0.3
            }
            
            response = requests.post(
                'https://api.openai.com/v1/chat/completions',
                headers=headers,
                json=data,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content'].strip()
                return content
                
        except Exception as e:
            print(f"OpenAI API 錯誤：{e}")
        
        return None

    def _try_claude(self, user_prompt: str, qr_type: str) -> Optional[str]:
        """嘗試使用 Claude API"""
        try:
            headers = {
                'x-api-key': self.claude_api_key,
                'Content-Type': 'application/json',
                'anthropic-version': '2023-06-01'
            }
            
            data = {
                'model': 'claude-3-haiku-20240307',
                'max_tokens': 100,
                'messages': [
                    {
                        'role': 'user', 
                        'content': f"{self.system_prompt}\n\n類型提示：{qr_type}\n用戶需求：{user_prompt}"
                    }
                ]
            }
            
            response = requests.post(
                'https://api.anthropic.com/v1/messages',
                headers=headers,
                json=data,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result['content'][0]['text'].strip()
                return content
                
        except Exception as e:
            print(f"Claude API 錯誤：{e}")
        
        return None

    def _try_local_ai(self, user_prompt: str, qr_type: str) -> Optional[str]:
        """嘗試使用本地 AI 模型 (Ollama)"""
        try:
            data = {
                'model': 'llama2',  # 或其他本地模型
                'prompt': f"{self.system_prompt}\n\n類型提示：{qr_type}\n用戶需求：{user_prompt}",
                'stream': False,
                'options': {
                    'temperature': 0.3,
                    'num_predict': 50
                }
            }
            
            response = requests.post(
                f'{self.local_api_url}/api/generate',
                json=data,
                timeout=15
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result['response'].strip()
                return content
                
        except Exception as e:
            print(f"本地 AI 錯誤：{e}")
        
        return None

    def _fallback_rule_engine(self, user_prompt: str, qr_type: str) -> Optional[str]:
        """備用規則引擎（不依賴外部 AI）"""
        import re
        
        prompt_lower = user_prompt.lower()
        
        # 學生座號識別
        if "學生" in user_prompt or "座號" in user_prompt or qr_type == "student":
            class_match = re.search(r'(\d+)[年班級]?', user_prompt)
            seat_match = re.search(r'(\d+)[號座]', user_prompt)
            
            class_num = class_match.group(1) if class_match else "1"
            seat_num = seat_match.group(1) if seat_match else "1"
            
            return f"{class_num}-{seat_num.zfill(2)}"
        
        # 作業識別
        elif "作業" in user_prompt or "習作" in user_prompt or qr_type == "assignment":
            if "數學" in user_prompt:
                page_match = re.search(r'(\d+)', user_prompt)
                page = page_match.group(1) if page_match else "25"
                return f"數學習作P{page}"
            elif "國語" in user_prompt:
                return "國語作業簿"
            elif "英語" in user_prompt or "英文" in user_prompt:
                return "英語練習本"
            else:
                return "作業"
        
        # 完整格式識別
        elif "繳交" in user_prompt or "hw" in prompt_lower or qr_type == "full":
            class_match = re.search(r'(\d+)[年班級]?', user_prompt)
            seat_match = re.search(r'(\d+)[號座]', user_prompt)
            
            class_num = class_match.group(1) if class_match else "1"
            seat_num = seat_match.group(1) if seat_match else "1"
            student_id = f"{class_num}-{seat_num.zfill(2)}"
            
            if "數學" in user_prompt:
                page_match = re.search(r'(\d+)', user_prompt)
                page = page_match.group(1) if page_match else "25"
                assignment = f"數學習作P{page}"
            elif "國語" in user_prompt:
                assignment = "國語作業簿"
            elif "英語" in user_prompt or "英文" in user_prompt:
                assignment = "英語練習本"
            else:
                assignment = "作業"
            
            return f"HW|{student_id}|{assignment}"
        
        # 庫存盤點識別
        elif "盤點" in user_prompt or "庫存" in user_prompt or qr_type == "inventory":
            qty_match = re.search(r'(\d+)[個件支張]', user_prompt)
            qty = qty_match.group(1) if qty_match else "1"
            
            if "鉛筆" in user_prompt:
                return f"INV|2B鉛筆|{qty}"
            elif "橡皮擦" in user_prompt:
                return f"INV|橡皮擦|{qty}"
            else:
                return f"INV|物品|{qty}"
        
        # 直接返回（如果已經是正確格式）
        elif "|" in user_prompt:
            return user_prompt
        
        # 默認處理
        return user_prompt

# 全域 AI 生成器實例
ai_generator = AIQRGenerator()

def ai_parse_prompt_advanced(prompt: str, qr_type: str = "auto") -> Optional[str]:
    """
    進階 AI 解析函數，供 Flask 應用使用
    """
    return ai_generator.generate_qr_content(prompt, qr_type)
