# 📚 小學作業掃描系統使用指南

## 🎯 專為小學老師設計的功能

### ✅ 主要優點
- **即時掌握**：立刻知道誰交了作業、誰還沒交
- **減少遺漏**：系統自動記錄，不會漏掉任何一個學生
- **省時高效**：不用一個一個點名檢查作業
- **家長溝通**：可以精確告知家長孩子的繳交狀況

## 📱 使用流程

### 1. 老師準備工作
1. 啟動系統（電腦執行 `python app.py`）
2. 手機連接同一個 Wi-Fi
3. 手機瀏覽器開啟系統網址

### 2. 學生繳交作業
學生將作業交給老師時，老師用手機掃描：
```
HW|座號|作業名稱
```

**範例 QR 碼內容：**
- `HW|1-05|數學習作P25` （1年5號繳交數學習作第25頁）
- `HW|1-12|國語作業簿` （1年12號繳交國語作業簿）

### 3. 即時查看統計
- **✅ 今日已繳交**：看誰已經交了作業
- **❌ 未繳交清單**：看誰還沒交（最重要！）
- **📊 班級統計**：整體繳交率統計
- **📦 庫存彙總**：教具用品盤點

## 🏷️ QR 碼製作建議

### 學生座號 QR 碼
為每個學生製作專屬的座號 QR 碼貼紙：
- `1-01` （1年1號）
- `1-02` （1年2號）
- `1-03` （1年3號）
- ...

### 作業類型 QR 碼
為常用作業製作 QR 碼：
- `數學習作P25`
- `國語作業簿`
- `自然觀察日記`
- `英語練習本`

### 實際操作
1. 學生繳交作業時說：「老師，我要交數學習作」
2. 老師掃描：學生座號 QR 碼 + 作業類型 QR 碼
3. 系統自動記錄：`HW|1-05|數學習作P25`
4. 手機震動提醒，顯示「✅ 作業登記 OK」

## 📊 老師最愛的功能

### 未繳交清單 ❌
點擊「未繳交清單」立刻看到：
```json
[
  {"student_id": "1-03", "student_name": "張小美", "assignment_title": "數學習作第25頁"},
  {"student_id": "1-07", "student_name": "劉小強", "assignment_title": "數學習作第25頁"}
]
```

### 班級統計 📊
```json
[
  {
    "assignment_title": "數學習作第25頁",
    "total_students": 25,
    "submitted_count": 23,
    "missing_count": 2
  }
]
```

## 🎨 使用技巧

### 1. QR 碼貼紙製作
- 用 QR 碼產生器製作各種貼紙
- 學生座號貼在學生證或姓名牌上
- 作業類型貼在黑板或作業收集箱上

### 2. 防重複機制
- 同一學生同一作業同一天只記錄一次
- 避免學生重複掃描造成混亂

### 3. 手動輸入備援
- QR 碼損壞時可手動輸入
- 格式：`HW|1-05|數學習作P25`

### 4. 家長溝通
- 可以精確告知：「小明今天的數學習作還沒交喔」
- 不會有「我明明有交」的爭議

## 🔧 系統設定建議

### 學生資料匯入
可以修改 `app.py` 中的學生資料：
```python
students_data = [
    ("1-01", "王小明"), ("1-02", "李小華"), 
    ("1-03", "張小美"), ("1-04", "陳小強"),
    # ... 加入你的班級學生
]
```

### 作業類型設定
```python
assignments_data = [
    ("數學習作P25", "數學習作第25頁", "2025-09-10"),
    ("國語作業簿", "國語作業簿第10課", "2025-09-12"),
    # ... 加入你的作業類型
]
```

## 💡 進階應用

1. **多班級管理**：座號格式改為 `3A-05`（3年A班5號）
2. **作業評分**：可擴充加入評分功能
3. **家長通知**：結合 LINE Bot 自動通知家長
4. **統計報表**：匯出 Excel 給學校行政

這個系統讓作業收集變得超級簡單，老師再也不用擔心漏掉任何學生的作業了！
