# 📱 掃描問題排除指南

## 🚨 「掃描後沒辦法掃進去」問題解決

### 📋 問題檢查清單

#### 1. **確認系統狀態**
- [ ] 系統正在運行（終端機顯示 "Running on https://192.168.1.103:9000"）
- [ ] 手機和電腦在同一個 Wi-Fi 網路
- [ ] 手機瀏覽器已開啟：`https://192.168.1.103:9000`
- [ ] 已接受 HTTPS 安全憑證警告

#### 2. **確認頁面載入**
- [ ] 看到「QR 掃描（作業繳交／庫存盤點）」標題
- [ ] 看到相機選擇下拉選單
- [ ] 看到「開始掃描」和「停止」按鈕
- [ ] 看到手動輸入欄位

#### 3. **確認相機權限**
- [ ] 點擊「開始掃描」後瀏覽器詢問相機權限
- [ ] 已點擊「允許」使用相機
- [ ] 相機畫面正常顯示
- [ ] 沒有出現「Camera not found」錯誤

### 🔧 逐步排除方法

#### 步驟 1：測試手動輸入
**目的**：確認系統後端是否正常工作

1. 在「手動輸入」欄位輸入：`HW|1-05|數學習作P25`
2. 點擊「送出」按鈕
3. **預期結果**：應該看到「✅ 作業登記 OK」訊息

**如果手動輸入失敗**：
- 檢查網址是否正確：`https://192.168.1.103:9000`
- 重新整理頁面
- 檢查系統是否還在運行

#### 步驟 2：測試相機功能
**目的**：確認相機是否正常開啟

1. 點擊「開始掃描」
2. **預期結果**：應該看到相機畫面
3. **如果沒有相機畫面**：
   - 檢查瀏覽器權限設定
   - 嘗試不同的瀏覽器
   - 參考「相機問題排除.md」

#### 步驟 3：測試 QR 碼掃描
**目的**：確認 QR 碼是否能被識別

1. 使用電腦螢幕顯示一個測試 QR 碼
2. 用手機掃描電腦螢幕上的 QR 碼
3. **測試 QR 碼內容**：`HW|1-05|數學習作P25`

### 🎯 常見問題與解決方案

#### 問題 A：相機開啟但掃描無反應
**症狀**：相機畫面正常，但掃描 QR 碼沒有任何反應

**解決方案**：
1. **檢查 QR 碼品質**
   - QR 碼是否清晰
   - 大小是否足夠（至少 2x2 公分）
   - 對比度是否足夠（黑白分明）

2. **調整掃描環境**
   - 充足光線
   - 保持手機穩定
   - 調整掃描距離（10-20 公分）

3. **檢查 QR 碼內容格式**
   - 確認格式正確：`HW|學號|作業名稱`
   - 避免特殊符號或空格

#### 問題 B：掃描成功但沒有回應
**症狀**：聽到掃描音效或震動，但系統沒有顯示成功訊息

**解決方案**：
1. **檢查網路連線**
   - 手機是否還連在同一個 Wi-Fi
   - 嘗試重新整理頁面

2. **檢查系統狀態**
   - 確認後端系統還在運行
   - 檢查終端機是否有錯誤訊息

3. **檢查 QR 碼內容**
   - 確認內容符合系統格式要求
   - 嘗試手動輸入相同內容測試

#### 問題 C：掃描後出現錯誤訊息
**症狀**：掃描後顯示「⚠️ 未識別內容」或其他錯誤

**解決方案**：
1. **檢查 QR 碼格式**
   - 作業繳交：`HW|學號|作業名稱`
   - 庫存盤點：`INV|品項代碼|數量`

2. **檢查內容完整性**
   - 確保有正確的分隔符號 `|`
   - 確保所有必要欄位都有填寫

### 🧪 測試用 QR 碼

#### 測試 QR 碼 1：作業繳交
```
內容：HW|1-05|數學習作P25
用途：測試作業繳交功能
```

#### 測試 QR 碼 2：庫存盤點
```
內容：INV|ITEM-001|3
用途：測試庫存盤點功能
```

### 📱 建立測試 QR 碼的方法

#### 方法 1：使用 QR內容產生器
1. 開啟 `QR內容產生器.html`
2. 選擇「完整作業繳交」
3. 填入：學生座號 `1-05`，作業名稱 `數學習作P25`
4. 複製產生的內容：`HW|1-05|數學習作P25`
5. 用手機 QR 碼產生器 APP 產生 QR 碼

#### 方法 2：線上產生器
1. 開啟 https://www.qr-code-generator.com/
2. 選擇「Text」
3. 輸入：`HW|1-05|數學習作P25`
4. 產生並下載 QR 碼

### 🔄 完整測試流程

1. **準備測試 QR 碼**
   - 產生一個包含 `HW|1-05|數學習作P25` 的 QR 碼
   - 列印或在另一個裝置上顯示

2. **測試手動輸入**
   - 在手動輸入欄位輸入：`HW|1-05|數學習作P25`
   - 點擊「送出」
   - 確認看到成功訊息

3. **測試掃描功能**
   - 點擊「開始掃描」
   - 掃描準備好的 QR 碼
   - 確認看到成功訊息

4. **檢查記錄**
   - 點擊「✅ 今日已繳交」
   - 確認看到剛才的記錄

### 🆘 緊急替代方案

如果掃描功能完全無法使用：

1. **使用手動輸入**
   - 學生口頭報告座號和作業名稱
   - 老師手動輸入格式：`HW|座號|作業名稱`

2. **使用其他掃描 APP**
   - 用一般的 QR 碼掃描 APP 掃描
   - 複製掃描結果
   - 貼到手動輸入欄位

3. **暫時紙本記錄**
   - 使用傳統紙本記錄
   - 事後批量輸入系統

記住：掃描只是輔助功能，系統的核心作業記錄功能不受影響！
