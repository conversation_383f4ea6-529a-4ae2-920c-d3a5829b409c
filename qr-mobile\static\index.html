<!doctype html>
<html lang="zh-Hant">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1"/>
  <title>QR 掃描（作業繳交／庫存盤點）</title>
  <!-- html5-qrcode CDN -->
  <script src="https://unpkg.com/html5-qrcode" defer></script>
  <style>
    body { font-family: system-ui, -apple-system, "Segoe UI", Roboto, "Noto Sans TC", sans-serif; margin: 16px; }
    h1 { font-size: 1.2rem; margin-bottom: 8px; }
    #reader { width: 100%; max-width: 480px; margin: 0 auto; }
    .row { display: flex; gap: 8px; margin-top: 8px; flex-wrap: wrap; }
    input, button, select { font-size: 1rem; padding: 8px; }
    #log { margin-top: 12px; white-space: pre-wrap; background: #f7f7f7; padding: 8px; border-radius: 6px; min-height: 48px; }
    a.btn { display:inline-block; padding:8px 12px; text-decoration:none; border:1px solid #ccc; border-radius:6px; }
  </style>
</head>
<body>
  <h1>QR 掃描（作業繳交／庫存盤點）</h1>

  <div class="row">
    <select id="cameraSelect"></select>
    <button id="startBtn">開始掃描</button>
    <button id="stopBtn" disabled>停止</button>
  </div>

  <div id="reader" style="margin-top:10px;"></div>

  <div class="row">
    <input id="manualText" placeholder="手動輸入：如 HW|S12345|HW3 或 INV|ITEM-001|3" style="flex:1;"/>
    <button id="sendBtn">送出</button>
  </div>

  <div id="log"></div>

  <div class="row">
    <a class="btn" href="/api/stats/submissions_today" target="_blank">✅ 今日已繳交</a>
    <a class="btn" href="/api/stats/missing_submissions" target="_blank">❌ 未繳交清單</a>
  </div>

  <div class="row">
    <a class="btn" href="/api/stats/class_summary" target="_blank">📊 班級統計</a>
    <a class="btn" href="/api/stats/stock_summary" target="_blank">📦 庫存彙總</a>
  </div>

<script>
let html5QrCode;
let lastData = "";
let lastTime = 0;

const cameraSelect = document.getElementById('cameraSelect');
const startBtn = document.getElementById('startBtn');
const stopBtn = document.getElementById('stopBtn');
const logEl = document.getElementById('log');

function log(msg) {
  const now = new Date().toLocaleTimeString();
  logEl.textContent = `[${now}] ${msg}\n` + logEl.textContent;
}

async function listCameras() {
  const devices = await Html5Qrcode.getCameras();
  cameraSelect.innerHTML = "";
  devices.forEach((d, idx) => {
    const opt = document.createElement('option');
    opt.value = d.id;
    opt.textContent = d.label || `相機 ${idx+1}`;
    cameraSelect.appendChild(opt);
  });
}

async function start() {
  if (!cameraSelect.value) await listCameras();
  const cameraId = cameraSelect.value;
  html5QrCode = new Html5Qrcode(/* element id */ "reader");
  startBtn.disabled = true;
  stopBtn.disabled = false;

  const config = { fps: 10, qrbox: { width: 250, height: 250 }, aspectRatio: 1.7778 };
  await html5QrCode.start(
    { deviceId: { exact: cameraId } },
    config,
    onScanSuccess,
    (err)=>{} // 忽略連續 decode 錯誤
  );
}

async function stop() {
  try {
    await html5QrCode.stop();
    await html5QrCode.clear();
  } catch(e) {}
  startBtn.disabled = false;
  stopBtn.disabled = true;
}

async function onScanSuccess(decodedText) {
  const now = Date.now();
  // 2 秒內相同內容不重覆送
  if (decodedText === lastData && now - lastTime < 2000) return;
  lastData = decodedText; lastTime = now;

  // 輕震提醒
  if (navigator.vibrate) navigator.vibrate(80);

  sendPayload(decodedText);
}

async function sendPayload(text) {
  try {
    const res = await fetch("/api/scan", {
      method:"POST",
      headers: { "Content-Type":"application/json" },
      body: JSON.stringify({ data: text })
    });
    const j = await res.json();
    if (res.ok && j.ok) {
      log("✅ " + j.message);
    } else {
      log("⚠️ " + (j.message || "未知錯誤"));
    }
  } catch (e) {
    log("❌ 連線失敗：" + e.message);
  }
}

document.getElementById('sendBtn').addEventListener('click', ()=>{
  const v = document.getElementById('manualText').value.trim();
  if (v) sendPayload(v);
});

startBtn.addEventListener('click', start);
stopBtn.addEventListener('click', stop);

window.addEventListener('load', listCameras);
</script>
</body>
</html>
