# 🔧 QR 碼產生器問題排除

## 🚨 常見問題

### 1. **QR 碼沒有顯示**

#### 可能原因：
- 網路連線問題，無法載入 QR 碼庫
- JavaScript 被瀏覽器阻擋
- CDN 服務暫時無法使用

#### 解決方案：
1. **檢查網路連線**
   - 確保電腦可以正常上網
   - 嘗試重新整理頁面

2. **使用簡易版產生器**
   - 開啟 `簡易QR產生器.html`
   - 使用 Google Charts API，更穩定可靠

3. **檢查瀏覽器設定**
   - 確保 JavaScript 已啟用
   - 暫時關閉廣告攔截器

### 2. **QR 碼產生失敗**

#### 錯誤訊息：「產生失敗」

#### 解決方案：
1. **檢查輸入內容**
   - 確保所有必填欄位都有填寫
   - 避免使用特殊符號

2. **重新載入頁面**
   - 按 F5 或 Ctrl+R 重新整理
   - 清除瀏覽器快取

3. **使用替代方案**
   - 使用線上 QR 碼產生器
   - 手機 APP：「QR Code Generator」

### 3. **QR 碼無法掃描**

#### 可能原因：
- QR 碼太小或模糊
- 內容格式不正確
- 掃描環境光線不足

#### 解決方案：
1. **調整 QR 碼大小**
   - 列印時放大到至少 2x2 公分
   - 確保圖片清晰

2. **檢查內容格式**
   - 學生座號：`1-05`
   - 作業名稱：`數學習作P25`
   - 完整格式：`HW|1-05|數學習作P25`

3. **改善掃描環境**
   - 充足光線下掃描
   - 保持手機穩定
   - 調整掃描距離

## 🛠️ 使用不同的 QR 碼產生器

### 方案 A：QR 內容產生器（強烈推薦）
```
開啟：QR內容產生器.html
特色：產生 QR 碼內容文字，可複製到任何 QR 碼產生器使用
優點：完全離線、不依賴網路、100% 可用
```

### 方案 B：離線產生器
```
開啟：離線QR產生器.html
特色：純 JavaScript 實現，完全不需要網路
注意：產生的是模擬 QR 碼，僅供參考
```

### 方案 C：簡易版產生器
```
開啟：簡易QR產生器.html
特色：使用 Google Charts API，需要網路連線
```

### 方案 D：線上產生器
1. **QR Code Generator**
   - 網址：https://www.qr-code-generator.com/
   - 免費、無需註冊

2. **QR Code Monkey**
   - 網址：https://www.qrcode-monkey.com/
   - 支援自訂樣式

### 方案 E：手機 APP
1. **iPhone**：內建相機或「快捷指令」APP
2. **Android**：Google Lens 或 QR Code Generator APP

## 📋 手動建立 QR 碼內容

如果 QR 碼產生器都無法使用，可以手動準備內容：

### 學生座號清單
```
1-01, 1-02, 1-03, 1-04, 1-05
1-06, 1-07, 1-08, 1-09, 1-10
... (依此類推)
```

### 作業類型清單
```
數學習作P25
國語作業簿
自然觀察日記
英語練習本
美勞作品
```

### 完整作業繳交格式
```
HW|1-01|數學習作P25
HW|1-02|數學習作P25
HW|1-03|數學習作P25
... (依此類推)
```

## 🎯 最佳實踐

### 1. **批量產生**
- 一次產生整班學生的座號 QR 碼
- 列印在貼紙上，貼在學生證或桌牌

### 2. **分類管理**
- 學生座號 QR 碼：貼在學生用品上
- 作業類型 QR 碼：貼在黑板或收集箱

### 3. **備份方案**
- 準備手動輸入的內容清單
- 訓練學生報告座號和作業名稱

### 4. **測試驗證**
- 產生後先用手機掃描測試
- 確認內容正確再大量列印

## 💡 小技巧

1. **QR 碼大小**：至少 2x2 公分，確保掃描成功
2. **列印品質**：使用雷射印表機，避免噴墨模糊
3. **保護措施**：護貝或貼透明膠帶防止磨損
4. **顏色對比**：黑色 QR 碼配白色背景效果最佳
5. **備用標籤**：在 QR 碼旁邊加上文字標籤

## 🆘 緊急替代方案

如果所有 QR 碼產生器都無法使用：

1. **使用手動輸入功能**
   - 在掃描頁面直接輸入：`HW|1-05|數學習作P25`

2. **口頭報告系統**
   - 學生繳交作業時口頭報告座號和作業名稱
   - 老師手動輸入系統

3. **紙本記錄**
   - 暫時使用傳統紙本記錄
   - 事後補登入系統

記住：QR 碼只是輔助工具，系統的核心功能（作業記錄和統計）不受影響！
