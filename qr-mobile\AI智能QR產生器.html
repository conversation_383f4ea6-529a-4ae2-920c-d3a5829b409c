<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI 智能 QR 碼產生器</title>
    <style>
        body {
            font-family: system-ui, -apple-system, "Segoe UI", <PERSON><PERSON>, "Noto Sans TC", sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 10px;
            font-size: 2.2rem;
        }
        .subtitle {
            text-align: center;
            color: #7f8c8d;
            margin-bottom: 30px;
            font-size: 1.1rem;
        }
        .ai-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .ai-section h2 {
            margin-top: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #555;
        }
        .ai-section label {
            color: white;
        }
        input, textarea, select {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            box-sizing: border-box;
            transition: border-color 0.3s;
        }
        input:focus, textarea:focus, select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        .ai-input {
            background: rgba(255,255,255,0.9);
            border: 2px solid rgba(255,255,255,0.3);
        }
        .ai-input:focus {
            background: white;
            border-color: white;
        }
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: transform 0.2s, box-shadow 0.2s;
            width: 100%;
            margin-bottom: 10px;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        button:active {
            transform: translateY(0);
        }
        .ai-button {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            font-size: 18px;
            padding: 15px 30px;
        }
        .ai-button:hover {
            box-shadow: 0 5px 15px rgba(245, 87, 108, 0.4);
        }
        .result-section {
            margin-top: 30px;
            padding: 25px;
            background: #f8f9fa;
            border-radius: 10px;
            border: 1px solid #e9ecef;
            display: none;
        }
        .qr-display {
            text-align: center;
            margin: 20px 0;
        }
        .qr-image {
            max-width: 300px;
            border: 3px solid #667eea;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .qr-content {
            font-family: monospace;
            font-size: 16px;
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #ddd;
            margin: 15px 0;
            word-break: break-all;
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.loading {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .examples {
            background: #fff3cd;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
            border: 1px solid #ffeaa7;
        }
        .examples h3 {
            color: #856404;
            margin-top: 0;
        }
        .example-item {
            background: white;
            padding: 10px;
            margin: 8px 0;
            border-radius: 5px;
            border-left: 4px solid #ffc107;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .example-item:hover {
            background: #f8f9fa;
        }
        .copy-btn {
            background: #28a745;
            width: auto;
            display: inline-block;
            margin-left: 10px;
            padding: 8px 16px;
            font-size: 14px;
        }
        .copy-btn:hover {
            background: #218838;
            box-shadow: 0 3px 10px rgba(40, 167, 69, 0.4);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 AI 智能 QR 碼產生器</h1>
        <p class="subtitle">用自然語言描述，AI 自動生成對應的 QR 碼</p>
        
        <div class="ai-section">
            <h2>🧠 智能規則引擎</h2>
            <p style="margin-bottom: 15px; opacity: 0.9;">
                使用內建智能規則引擎，無需網路連線，即時解析您的自然語言需求
            </p>
            <div class="form-group">
                <label>告訴系統您想要什麼樣的 QR 碼：</label>
                <textarea id="aiPrompt" class="ai-input" rows="3"
                    placeholder="例如：幫我生成1年級5號學生的數學習作第25頁作業繳交QR碼"></textarea>
            </div>
            <div class="form-group">
                <label>QR 碼類型提示（可選）：</label>
                <select id="qrTypeHint" class="ai-input">
                    <option value="auto">讓系統自動判斷</option>
                    <option value="student">學生座號</option>
                    <option value="assignment">作業名稱</option>
                    <option value="full">完整作業繳交</option>
                    <option value="inventory">庫存盤點</option>
                </select>
            </div>
            <button class="ai-button" onclick="generateWithAI()">🚀 智能生成 QR 碼</button>
        </div>

        <div id="status"></div>
        
        <div id="resultSection" class="result-section">
            <h3>✨ AI 生成結果</h3>
            <div class="qr-display">
                <img id="qrImage" class="qr-image" style="display: none;">
            </div>
            <div class="form-group">
                <label>QR 碼內容：</label>
                <div id="qrContent" class="qr-content"></div>
                <button class="copy-btn" onclick="copyContent()">📋 複製內容</button>
            </div>
        </div>

        <div class="examples">
            <h3>💡 AI 提示範例</h3>
            <p>點擊下方範例可以快速填入：</p>
            
            <div class="example-item" onclick="fillPrompt('幫我生成1年級5號學生的數學習作第25頁作業繳交QR碼')">
                📚 幫我生成1年級5號學生的數學習作第25頁作業繳交QR碼
            </div>
            
            <div class="example-item" onclick="fillPrompt('生成2年級12號學生座號QR碼')">
                👤 生成2年級12號學生座號QR碼
            </div>
            
            <div class="example-item" onclick="fillPrompt('製作國語作業簿的QR碼')">
                📖 製作國語作業簿的QR碼
            </div>
            
            <div class="example-item" onclick="fillPrompt('盤點5支2B鉛筆的庫存QR碼')">
                📦 盤點5支2B鉛筆的庫存QR碼
            </div>
            
            <div class="example-item" onclick="fillPrompt('3年級全班的英語練習本繳交')">
                🏫 3年級全班的英語練習本繳交
            </div>
        </div>

        <div style="margin-top: 30px; padding: 20px; background: #e9ecef; border-radius: 8px;">
            <h3>🔧 智能功能說明</h3>
            <ul>
                <li><strong>智能解析</strong>：內建規則引擎自動識別您的需求並生成對應格式</li>
                <li><strong>自然語言</strong>：用平常說話的方式描述即可，無需記憶複雜格式</li>
                <li><strong>多種格式</strong>：支援學生座號、作業名稱、完整繳交、庫存盤點</li>
                <li><strong>即時生成</strong>：本地處理，立即生成可用的 QR 碼圖片</li>
                <li><strong>離線工作</strong>：不依賴網路 API，完全本地化處理</li>
                <li><strong>一鍵複製</strong>：可複製內容到其他 QR 碼產生器使用</li>
            </ul>

            <div style="margin-top: 15px; padding: 15px; background: #d1ecf1; border-radius: 5px; border-left: 4px solid #17a2b8;">
                <strong>💡 提示：</strong>系統使用內建智能規則引擎，無需設定任何 API 金鑰即可使用。
                如需更強大的 AI 功能，可參考「AI設定指南.md」進行進階設定。
            </div>
        </div>
    </div>

    <script>
        function fillPrompt(text) {
            document.getElementById('aiPrompt').value = text;
        }

        async function generateWithAI() {
            const prompt = document.getElementById('aiPrompt').value.trim();
            const typeHint = document.getElementById('qrTypeHint').value;

            if (!prompt) {
                showStatus('請輸入您的需求描述', 'error');
                return;
            }

            showStatus('🤖 智能規則引擎正在分析您的需求...', 'loading');

            try {
                const response = await fetch('/api/ai/generate-qr', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        prompt: prompt,
                        type: typeHint
                    })
                });

                const result = await response.json();

                if (response.ok && result.ok) {
                    const method = result.method || '智能規則引擎';
                    showStatus(`✅ ${method}成功解析：${result.content}`, 'success');
                    displayResult(result.content, result.image);
                } else {
                    showStatus('❌ ' + (result.message || '生成失敗，請嘗試更具體的描述'), 'error');
                    showSuggestions(prompt);
                }

            } catch (error) {
                console.error('請求錯誤：', error);
                showStatus('❌ 系統錯誤，請檢查網路連線或重新整理頁面', 'error');
                showSuggestions(prompt);
            }
        }

        function showSuggestions(originalPrompt) {
            const suggestions = [
                '請嘗試更具體的描述，例如：',
                '• "1年級5號學生的數學習作第25頁作業繳交"',
                '• "生成2年級12號學生座號"',
                '• "製作國語作業簿的QR碼"',
                '• "盤點5支2B鉛筆"'
            ];

            setTimeout(() => {
                const statusDiv = document.getElementById('status');
                statusDiv.innerHTML = `
                    <div class="status error">
                        ${statusDiv.textContent}<br><br>
                        ${suggestions.join('<br>')}
                    </div>
                `;
            }, 2000);
        }

        function displayResult(content, imageBase64) {
            document.getElementById('qrContent').textContent = content;
            
            if (imageBase64) {
                const img = document.getElementById('qrImage');
                img.src = imageBase64;
                img.style.display = 'block';
            }
            
            document.getElementById('resultSection').style.display = 'block';
            
            // 滾動到結果區域
            document.getElementById('resultSection').scrollIntoView({ 
                behavior: 'smooth' 
            });
        }

        function copyContent() {
            const content = document.getElementById('qrContent').textContent;
            navigator.clipboard.writeText(content).then(() => {
                showStatus('📋 已複製到剪貼簿：' + content, 'success');
            }).catch(() => {
                // 備用複製方法
                const textArea = document.createElement('textarea');
                textArea.value = content;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showStatus('📋 已複製到剪貼簿：' + content, 'success');
            });
        }

        function showStatus(message, type) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
            statusDiv.style.display = 'block';
            
            if (type === 'success') {
                setTimeout(() => {
                    statusDiv.style.display = 'none';
                }, 5000);
            }
        }

        // 頁面載入時的歡迎訊息
        window.onload = function() {
            showStatus('🤖 AI 智能 QR 碼產生器已就緒！請描述您的需求', 'loading');
            setTimeout(() => {
                document.getElementById('status').style.display = 'none';
            }, 3000);
        };
    </script>
</body>
</html>
