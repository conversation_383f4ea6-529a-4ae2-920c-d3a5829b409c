<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QR 碼內容產生器</title>
    <style>
        body {
            font-family: system-ui, -apple-system, "Segoe UI", <PERSON><PERSON>, "Noto Sans TC", sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #e3f2fd;
            border-radius: 8px;
            background: #fafafa;
        }
        .section h2 {
            color: #1976d2;
            margin-top: 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, textarea, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        button {
            background: #1976d2;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #1565c0;
        }
        .copy-btn {
            background: #28a745;
        }
        .copy-btn:hover {
            background: #218838;
        }
        .result-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 15px;
            max-height: 300px;
            overflow-y: auto;
        }
        .qr-item {
            background: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .qr-content {
            font-family: monospace;
            font-size: 14px;
            flex-grow: 1;
            margin-right: 10px;
            word-break: break-all;
        }
        .notice {
            background: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border: 1px solid #ffeaa7;
        }
        .instructions {
            background: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            border: 1px solid #bee5eb;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📝 QR 碼內容產生器</h1>
        
        <div class="notice">
            <strong>💡 使用說明</strong><br>
            此工具產生 QR 碼內容文字，您可以複製這些內容到任何 QR 碼產生器（如手機 APP 或線上工具）來建立實際的 QR 碼。
        </div>

        <div class="grid">
            <!-- 學生座號產生器 -->
            <div class="section">
                <h2>👥 學生座號</h2>
                <div class="form-group">
                    <label>班級：</label>
                    <input type="text" id="studentClass" placeholder="例如：1、2A、3B" value="1">
                </div>
                <div class="form-group">
                    <label>學生人數：</label>
                    <input type="number" id="studentCount" placeholder="班級總人數" value="30" min="1" max="50">
                </div>
                <button onclick="generateStudentList()">產生學生座號清單</button>
                <button class="copy-btn" onclick="copyAllStudents()">複製全部</button>
                <div id="studentResult" class="result-area" style="display: none;"></div>
            </div>

            <!-- 作業類型產生器 -->
            <div class="section">
                <h2>📝 作業類型</h2>
                <div class="form-group">
                    <label>作業清單（每行一個）：</label>
                    <textarea id="assignmentList" rows="6" placeholder="請輸入作業名稱，每行一個">數學習作P25
國語作業簿
自然觀察日記
英語練習本
美勞作品
體育心得</textarea>
                </div>
                <button onclick="generateAssignmentList()">產生作業類型清單</button>
                <button class="copy-btn" onclick="copyAllAssignments()">複製全部</button>
                <div id="assignmentResult" class="result-area" style="display: none;"></div>
            </div>
        </div>

        <!-- 完整作業繳交產生器 -->
        <div class="section">
            <h2>✅ 完整作業繳交格式</h2>
            <div class="grid">
                <div class="form-group">
                    <label>班級：</label>
                    <input type="text" id="fullClass" placeholder="例如：1" value="1">
                </div>
                <div class="form-group">
                    <label>學生人數：</label>
                    <input type="number" id="fullStudentCount" placeholder="班級總人數" value="30" min="1" max="50">
                </div>
            </div>
            <div class="form-group">
                <label>作業名稱：</label>
                <input type="text" id="fullAssignment" placeholder="例如：數學習作P25" value="數學習作P25">
            </div>
            <button onclick="generateFullList()">產生完整格式清單</button>
            <button class="copy-btn" onclick="copyAllFull()">複製全部</button>
            <div id="fullResult" class="result-area" style="display: none;"></div>
        </div>

        <!-- 自訂內容產生器 -->
        <div class="section">
            <h2>🔧 自訂內容</h2>
            <div class="form-group">
                <label>自訂內容：</label>
                <textarea id="customContent" rows="3" placeholder="輸入任何想要產生 QR 碼的內容"></textarea>
            </div>
            <button onclick="generateCustom()">產生自訂 QR 碼內容</button>
            <button class="copy-btn" onclick="copyCustom()">複製內容</button>
            <div id="customResult" class="result-area" style="display: none;"></div>
        </div>

        <div class="instructions">
            <h3>📱 如何使用產生的內容：</h3>
            <ol>
                <li><strong>複製內容</strong>：點擊「複製」按鈕複製 QR 碼內容</li>
                <li><strong>開啟 QR 碼產生器</strong>：
                    <ul>
                        <li>手機：搜尋「QR Code Generator」APP</li>
                        <li>線上：Google 搜尋「QR code generator」</li>
                        <li>推薦：qr-code-generator.com</li>
                    </ul>
                </li>
                <li><strong>貼上內容</strong>：將複製的內容貼到 QR 碼產生器中</li>
                <li><strong>產生並儲存</strong>：產生 QR 碼並儲存圖片</li>
                <li><strong>列印使用</strong>：列印 QR 碼貼紙，貼在相應位置</li>
            </ol>
        </div>
    </div>

    <script>
        function generateStudentList() {
            const classNum = document.getElementById('studentClass').value;
            const studentCount = parseInt(document.getElementById('studentCount').value);
            const container = document.getElementById('studentResult');
            
            if (!classNum || !studentCount) {
                alert('請填入班級和學生人數');
                return;
            }
            
            let html = '';
            for (let i = 1; i <= studentCount; i++) {
                const studentId = `${classNum}-${i.toString().padStart(2, '0')}`;
                html += `
                    <div class="qr-item">
                        <div class="qr-content">${studentId}</div>
                        <button onclick="copyText('${studentId}')">複製</button>
                    </div>
                `;
            }
            
            container.innerHTML = html;
            container.style.display = 'block';
        }

        function generateAssignmentList() {
            const assignmentText = document.getElementById('assignmentList').value;
            const assignments = assignmentText.split('\n').filter(line => line.trim());
            const container = document.getElementById('assignmentResult');
            
            if (assignments.length === 0) {
                alert('請輸入作業清單');
                return;
            }
            
            let html = '';
            assignments.forEach(assignment => {
                const trimmed = assignment.trim();
                html += `
                    <div class="qr-item">
                        <div class="qr-content">${trimmed}</div>
                        <button onclick="copyText('${trimmed}')">複製</button>
                    </div>
                `;
            });
            
            container.innerHTML = html;
            container.style.display = 'block';
        }

        function generateFullList() {
            const classNum = document.getElementById('fullClass').value;
            const studentCount = parseInt(document.getElementById('fullStudentCount').value);
            const assignment = document.getElementById('fullAssignment').value;
            const container = document.getElementById('fullResult');
            
            if (!classNum || !studentCount || !assignment) {
                alert('請填入所有必要資訊');
                return;
            }
            
            let html = '';
            for (let i = 1; i <= studentCount; i++) {
                const studentId = `${classNum}-${i.toString().padStart(2, '0')}`;
                const fullContent = `HW|${studentId}|${assignment}`;
                html += `
                    <div class="qr-item">
                        <div class="qr-content">${fullContent}</div>
                        <button onclick="copyText('${fullContent}')">複製</button>
                    </div>
                `;
            }
            
            container.innerHTML = html;
            container.style.display = 'block';
        }

        function generateCustom() {
            const content = document.getElementById('customContent').value;
            const container = document.getElementById('customResult');
            
            if (!content) {
                alert('請輸入自訂內容');
                return;
            }
            
            container.innerHTML = `
                <div class="qr-item">
                    <div class="qr-content">${content}</div>
                    <button onclick="copyText('${content}')">複製</button>
                </div>
            `;
            container.style.display = 'block';
        }

        function copyText(text) {
            navigator.clipboard.writeText(text).then(() => {
                alert('已複製到剪貼簿：' + text);
            }).catch(() => {
                // 備用複製方法
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('已複製到剪貼簿：' + text);
            });
        }

        function copyAllStudents() {
            const classNum = document.getElementById('studentClass').value;
            const studentCount = parseInt(document.getElementById('studentCount').value);
            
            if (!classNum || !studentCount) {
                alert('請先產生學生座號清單');
                return;
            }
            
            let allContent = '';
            for (let i = 1; i <= studentCount; i++) {
                const studentId = `${classNum}-${i.toString().padStart(2, '0')}`;
                allContent += studentId + '\n';
            }
            
            copyText(allContent.trim());
        }

        function copyAllAssignments() {
            const assignmentText = document.getElementById('assignmentList').value;
            const assignments = assignmentText.split('\n').filter(line => line.trim());
            
            if (assignments.length === 0) {
                alert('請先產生作業類型清單');
                return;
            }
            
            copyText(assignments.join('\n'));
        }

        function copyAllFull() {
            const classNum = document.getElementById('fullClass').value;
            const studentCount = parseInt(document.getElementById('fullStudentCount').value);
            const assignment = document.getElementById('fullAssignment').value;
            
            if (!classNum || !studentCount || !assignment) {
                alert('請先產生完整格式清單');
                return;
            }
            
            let allContent = '';
            for (let i = 1; i <= studentCount; i++) {
                const studentId = `${classNum}-${i.toString().padStart(2, '0')}`;
                const fullContent = `HW|${studentId}|${assignment}`;
                allContent += fullContent + '\n';
            }
            
            copyText(allContent.trim());
        }

        function copyCustom() {
            const content = document.getElementById('customContent').value;
            if (!content) {
                alert('請先輸入自訂內容');
                return;
            }
            copyText(content);
        }

        // 頁面載入時產生範例
        window.onload = function() {
            generateStudentList();
            generateAssignmentList();
        };
    </script>
</body>
</html>
