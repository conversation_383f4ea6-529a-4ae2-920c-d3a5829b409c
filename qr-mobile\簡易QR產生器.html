<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>簡易 QR 碼產生器</title>
    <style>
        body {
            font-family: system-ui, -apple-system, "Segoe UI", <PERSON><PERSON>, "Noto Sans TC", sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #555;
        }
        input, textarea, select {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        button {
            background: #1976d2;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
            margin-bottom: 10px;
        }
        button:hover {
            background: #1565c0;
        }
        .qr-result {
            text-align: center;
            margin-top: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        .qr-result img {
            max-width: 100%;
            height: auto;
        }
        .qr-text {
            margin-top: 10px;
            font-family: monospace;
            background: #e9ecef;
            padding: 8px;
            border-radius: 4px;
            word-break: break-all;
        }
        .example {
            background: #e8f5e8;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            font-size: 14px;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📱 簡易 QR 碼產生器</h1>
        
        <div class="form-group">
            <label>QR 碼類型：</label>
            <select id="qrType" onchange="updateForm()">
                <option value="student">學生座號</option>
                <option value="assignment">作業名稱</option>
                <option value="full">完整作業繳交</option>
                <option value="custom">自訂內容</option>
            </select>
        </div>

        <!-- 學生座號表單 -->
        <div id="studentForm" class="form-section">
            <div class="form-group">
                <label>班級：</label>
                <input type="text" id="studentClass" placeholder="例如：1、2A、3B" value="1">
            </div>
            <div class="form-group">
                <label>座號：</label>
                <input type="number" id="studentNumber" placeholder="例如：5" value="5" min="1" max="50">
            </div>
        </div>

        <!-- 作業名稱表單 -->
        <div id="assignmentForm" class="form-section" style="display: none;">
            <div class="form-group">
                <label>作業名稱：</label>
                <input type="text" id="assignmentName" placeholder="例如：數學習作P25" value="數學習作P25">
            </div>
        </div>

        <!-- 完整作業繳交表單 -->
        <div id="fullForm" class="form-section" style="display: none;">
            <div class="form-group">
                <label>學生座號：</label>
                <input type="text" id="fullStudentId" placeholder="例如：1-05" value="1-05">
            </div>
            <div class="form-group">
                <label>作業名稱：</label>
                <input type="text" id="fullAssignmentName" placeholder="例如：數學習作P25" value="數學習作P25">
            </div>
        </div>

        <!-- 自訂內容表單 -->
        <div id="customForm" class="form-section" style="display: none;">
            <div class="form-group">
                <label>自訂內容：</label>
                <textarea id="customContent" rows="3" placeholder="輸入任何想要產生 QR 碼的內容"></textarea>
            </div>
        </div>

        <button onclick="generateQR()">產生 QR 碼</button>

        <div class="example">
            <strong>格式說明：</strong><br>
            • 學生座號：1-05<br>
            • 作業名稱：數學習作P25<br>
            • 完整格式：HW|1-05|數學習作P25
        </div>

        <div id="status"></div>
        <div id="qrResult" class="qr-result" style="display: none;"></div>
    </div>

    <script>
        function updateForm() {
            const qrType = document.getElementById('qrType').value;
            const forms = ['studentForm', 'assignmentForm', 'fullForm', 'customForm'];
            
            // 隱藏所有表單
            forms.forEach(formId => {
                document.getElementById(formId).style.display = 'none';
            });
            
            // 顯示對應表單
            switch(qrType) {
                case 'student':
                    document.getElementById('studentForm').style.display = 'block';
                    break;
                case 'assignment':
                    document.getElementById('assignmentForm').style.display = 'block';
                    break;
                case 'full':
                    document.getElementById('fullForm').style.display = 'block';
                    break;
                case 'custom':
                    document.getElementById('customForm').style.display = 'block';
                    break;
            }
        }

        function generateQR() {
            const qrType = document.getElementById('qrType').value;
            let qrData = '';
            
            // 根據類型產生 QR 碼內容
            switch(qrType) {
                case 'student':
                    const studentClass = document.getElementById('studentClass').value;
                    const studentNumber = document.getElementById('studentNumber').value;
                    if (!studentClass || !studentNumber) {
                        showStatus('請填入班級和座號', 'error');
                        return;
                    }
                    qrData = `${studentClass}-${studentNumber.padStart(2, '0')}`;
                    break;
                    
                case 'assignment':
                    qrData = document.getElementById('assignmentName').value;
                    if (!qrData) {
                        showStatus('請填入作業名稱', 'error');
                        return;
                    }
                    break;
                    
                case 'full':
                    const fullStudentId = document.getElementById('fullStudentId').value;
                    const fullAssignmentName = document.getElementById('fullAssignmentName').value;
                    if (!fullStudentId || !fullAssignmentName) {
                        showStatus('請填入學生座號和作業名稱', 'error');
                        return;
                    }
                    qrData = `HW|${fullStudentId}|${fullAssignmentName}`;
                    break;
                    
                case 'custom':
                    qrData = document.getElementById('customContent').value;
                    if (!qrData) {
                        showStatus('請填入自訂內容', 'error');
                        return;
                    }
                    break;
            }
            
            // 使用 Google Charts API 產生 QR 碼
            generateQRWithGoogleAPI(qrData);
        }

        function generateQRWithGoogleAPI(data) {
            showStatus('正在產生 QR 碼...', 'success');
            
            // 使用 Google Charts QR API
            const encodedData = encodeURIComponent(data);
            const qrUrl = `https://chart.googleapis.com/chart?chs=300x300&cht=qr&chl=${encodedData}&choe=UTF-8`;
            
            const resultDiv = document.getElementById('qrResult');
            resultDiv.innerHTML = `
                <img src="${qrUrl}" alt="QR Code" onload="showStatus('QR 碼產生成功！', 'success')" onerror="handleQRError()">
                <div class="qr-text">${data}</div>
                <p><small>右鍵點擊圖片可以儲存</small></p>
            `;
            resultDiv.style.display = 'block';
        }

        function handleQRError() {
            showStatus('QR 碼產生失敗，請檢查網路連線', 'error');
            document.getElementById('qrResult').style.display = 'none';
        }

        function showStatus(message, type) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
            statusDiv.style.display = 'block';
            
            if (type === 'success') {
                setTimeout(() => {
                    statusDiv.style.display = 'none';
                }, 3000);
            }
        }

        // 頁面載入時初始化
        window.onload = function() {
            updateForm();
            // 產生一個範例 QR 碼
            setTimeout(() => {
                generateQR();
            }, 500);
        };
    </script>
</body>
</html>
