# 手機網頁版 QR 掃描器

一個簡單易用的 QR 掃描系統，支援作業繳交和庫存盤點功能。手機和電腦在同一個 Wi-Fi 環境下即可使用。

## 功能特色

- 📱 **純網頁解決方案**：不需安裝 APP，手機瀏覽器即可使用
- 📚 **作業繳交**：掃描 `HW|學號|作業代碼` 格式的 QR 碼
- 📦 **庫存盤點**：掃描 `INV|品項代碼|數量` 格式的 QR 碼
- 🔒 **防重複掃描**：2 秒內相同內容不重複處理
- 📊 **即時統計**：查看今日繳交清單和庫存彙總
- 🌐 **區域網路**：手機和電腦同 Wi-Fi 即可使用

## 專案結構

```
qr-mobile/
├─ app.py                # Flask 後端 + SQLite 資料庫
├─ requirements.txt      # Python 依賴套件
├─ static/
│  └─ index.html        # 手機掃描頁面
└─ README.md            # 說明文件
```

## 🚀 一鍵啟動（推薦）

### Windows 用戶
- **圖形化介面**：雙擊 `圖形化啟動.bat`（推薦）
- **命令列介面**：雙擊 `啟動系統.bat`

### macOS/Linux 用戶
- **圖形化介面**：執行 `python3 圖形化啟動器.py`
- **命令列介面**：執行 `./啟動系統.sh`

### 手動安裝（進階用戶）

```bash
# 1. 建立虛擬環境
python -m venv .venv

# 2. 啟動虛擬環境
# Windows
.venv\Scripts\activate
# macOS/Linux
source .venv/bin/activate

# 3. 安裝依賴套件
pip install -r requirements.txt

# 4. 啟動服務
python app.py
```

服務啟動後會顯示：
```
* Running on all addresses (0.0.0.0)
* Running on http://127.0.0.1:5000
* Running on http://[你的電腦IP]:5000
```

### 4. 手機連線使用

1. 確保手機和電腦連接到同一個 Wi-Fi
2. 在手機瀏覽器開啟：`http://[你的電腦IP]:5000`
3. 允許瀏覽器使用相機權限
4. 開始掃描 QR 碼

## QR 碼格式

### 作業繳交
```
HW|學號|作業代碼
```
範例：`HW|S12345|HW3`

### 庫存盤點
```
INV|品項代碼|數量
```
範例：
- `INV|ITEM-001|3` （盤點 3 個）
- `INV|ITEM-001` （數量預設為 1）

## API 端點

- `GET /` - 手機掃描頁面
- `POST /api/scan` - 掃描資料處理
- `GET /api/stats/submissions_today` - 今日繳交清單
- `GET /api/stats/stock_summary` - 庫存彙總

## 資料庫

系統會自動建立 SQLite 資料庫 `school_ops.sqlite`，包含以下資料表：

- `students` - 學生資料
- `assignments` - 作業資料
- `submissions` - 繳交記錄（同學生同作業同日只記錄一次）
- `items` - 物品資料
- `stock_counts` - 庫存盤點記錄

## 使用技巧

1. **防重複機制**：同一學生、同一作業、同一天只會記錄一次繳交
2. **手動輸入**：如果 QR 碼損壞，可使用手動輸入功能
3. **即時回饋**：掃描成功會有震動提醒和訊息顯示
4. **統計查詢**：點擊統計連結可查看詳細資料

## 擴充功能建議

- CSV 匯出功能
- 遲交清單查詢
- 盤點差異報表
- 簡易權限控制
- 多語言支援
