<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小學作業 QR 碼產生器</title>
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    <!-- 備用 CDN -->
    <script>
        // 檢查 QRCode 是否載入成功，如果失敗則使用備用方案
        window.addEventListener('load', function() {
            if (typeof QRCode === 'undefined') {
                console.log('QRCode 庫載入失敗，嘗試載入備用 CDN...');
                const script = document.createElement('script');
                script.src = 'https://unpkg.com/qrcode@1.5.3/build/qrcode.min.js';
                script.onload = function() {
                    console.log('備用 QRCode 庫載入成功');
                    initializeQRGenerator();
                };
                script.onerror = function() {
                    console.error('所有 QRCode 庫都載入失敗');
                    alert('QR 碼庫載入失敗，請檢查網路連線或稍後再試');
                };
                document.head.appendChild(script);
            } else {
                console.log('QRCode 庫載入成功');
                initializeQRGenerator();
            }
        });
    </script>
    <style>
        body {
            font-family: system-ui, -apple-system, "Segoe UI", Roboto, "Noto Sans TC", sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 40px;
            padding: 20px;
            border: 2px solid #e3f2fd;
            border-radius: 8px;
            background: #fafafa;
        }
        .section h2 {
            color: #1976d2;
            margin-top: 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        button {
            background: #1976d2;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #1565c0;
        }
        .qr-output {
            text-align: center;
            margin-top: 20px;
            padding: 20px;
            background: white;
            border-radius: 8px;
        }
        .qr-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .qr-item {
            text-align: center;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: white;
        }
        .qr-item canvas {
            margin-bottom: 10px;
        }
        .qr-text {
            font-size: 14px;
            color: #666;
            word-break: break-all;
        }
        .example {
            background: #e8f5e8;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📚 小學作業 QR 碼產生器</h1>
        
        <!-- 學生座號 QR 碼 -->
        <div class="section">
            <h2>👥 學生座號 QR 碼</h2>
            <div class="form-group">
                <label>班級：</label>
                <input type="text" id="classNumber" placeholder="例如：1、2A、3B" value="1">
            </div>
            <div class="form-group">
                <label>學生人數：</label>
                <input type="number" id="studentCount" placeholder="班級總人數" value="30" min="1" max="50">
            </div>
            <button onclick="generateStudentQRs()">產生學生座號 QR 碼</button>
            <div class="example">
                範例格式：1-01, 1-02, 1-03... (1年級1號、2號、3號...)
            </div>
            <div id="studentQRs" class="qr-output"></div>
        </div>

        <!-- 作業類型 QR 碼 -->
        <div class="section">
            <h2>📝 作業類型 QR 碼</h2>
            <div class="form-group">
                <label>作業清單（每行一個）：</label>
                <textarea id="assignmentList" rows="8" placeholder="請輸入作業名稱，每行一個">數學習作P25
國語作業簿
自然觀察日記
英語練習本
美勞作品
體育心得
音樂欣賞單
社會學習單</textarea>
            </div>
            <button onclick="generateAssignmentQRs()">產生作業類型 QR 碼</button>
            <div class="example">
                這些 QR 碼可以貼在黑板上或作業收集箱上
            </div>
            <div id="assignmentQRs" class="qr-output"></div>
        </div>

        <!-- 完整作業繳交 QR 碼 -->
        <div class="section">
            <h2>✅ 完整作業繳交 QR 碼</h2>
            <div class="form-group">
                <label>學生座號：</label>
                <input type="text" id="fullStudentId" placeholder="例如：1-05" value="1-05">
            </div>
            <div class="form-group">
                <label>作業名稱：</label>
                <input type="text" id="fullAssignment" placeholder="例如：數學習作P25" value="數學習作P25">
            </div>
            <button onclick="generateFullQR()">產生完整 QR 碼</button>
            <div class="example">
                格式：HW|1-05|數學習作P25<br>
                這種 QR 碼包含完整資訊，一次掃描就完成記錄
            </div>
            <div id="fullQR" class="qr-output"></div>
        </div>

        <!-- 使用說明 -->
        <div class="section">
            <h2>📋 使用說明</h2>
            <ol>
                <li><strong>學生座號 QR 碼</strong>：為每個學生製作專屬貼紙，貼在學生證或桌牌上</li>
                <li><strong>作業類型 QR 碼</strong>：貼在黑板或作業收集箱上，方便老師快速選擇</li>
                <li><strong>完整 QR 碼</strong>：包含學生和作業資訊，適合特殊情況使用</li>
                <li><strong>列印建議</strong>：右鍵點擊 QR 碼圖片選擇「另存圖片」，然後列印</li>
                <li><strong>尺寸建議</strong>：QR 碼至少 2x2 公分，確保手機能清楚掃描</li>
            </ol>
        </div>
    </div>

    <script>
        // 產生學生座號 QR 碼
        function generateStudentQRs() {
            console.log('開始產生學生座號 QR 碼...');

            if (typeof QRCode === 'undefined') {
                console.error('QRCode 庫未載入');
                alert('QR 碼庫尚未載入完成，請稍後再試');
                return;
            }

            const classNum = document.getElementById('classNumber').value;
            const studentCount = parseInt(document.getElementById('studentCount').value);
            const container = document.getElementById('studentQRs');

            if (!classNum || !studentCount) {
                alert('請填入班級和學生人數');
                return;
            }

            container.innerHTML = '<div class="qr-grid"></div>';
            const grid = container.querySelector('.qr-grid');

            for (let i = 1; i <= studentCount; i++) {
                const studentId = `${classNum}-${i.toString().padStart(2, '0')}`;
                const qrItem = document.createElement('div');
                qrItem.className = 'qr-item';

                const canvas = document.createElement('canvas');
                QRCode.toCanvas(canvas, studentId, { width: 150 }, function (error) {
                    if (error) {
                        console.error('QR 碼產生錯誤:', error);
                        qrItem.innerHTML = `<div style="color: red;">產生失敗: ${studentId}</div>`;
                    } else {
                        console.log('成功產生 QR 碼:', studentId);
                    }
                });

                qrItem.appendChild(canvas);
                qrItem.innerHTML += `<div class="qr-text">座號：${studentId}</div>`;
                grid.appendChild(qrItem);
            }

            console.log(`完成產生 ${studentCount} 個學生座號 QR 碼`);
        }
        
        // 產生作業類型 QR 碼
        function generateAssignmentQRs() {
            console.log('開始產生作業類型 QR 碼...');

            if (typeof QRCode === 'undefined') {
                console.error('QRCode 庫未載入');
                alert('QR 碼庫尚未載入完成，請稍後再試');
                return;
            }

            const assignmentText = document.getElementById('assignmentList').value;
            const assignments = assignmentText.split('\n').filter(line => line.trim());
            const container = document.getElementById('assignmentQRs');

            if (assignments.length === 0) {
                alert('請輸入作業清單');
                return;
            }

            container.innerHTML = '<div class="qr-grid"></div>';
            const grid = container.querySelector('.qr-grid');

            assignments.forEach(assignment => {
                const qrItem = document.createElement('div');
                qrItem.className = 'qr-item';

                const canvas = document.createElement('canvas');
                QRCode.toCanvas(canvas, assignment.trim(), { width: 150 }, function (error) {
                    if (error) {
                        console.error('QR 碼產生錯誤:', error);
                        qrItem.innerHTML = `<div style="color: red;">產生失敗: ${assignment.trim()}</div>`;
                    } else {
                        console.log('成功產生 QR 碼:', assignment.trim());
                    }
                });

                qrItem.appendChild(canvas);
                qrItem.innerHTML += `<div class="qr-text">${assignment.trim()}</div>`;
                grid.appendChild(qrItem);
            });

            console.log(`完成產生 ${assignments.length} 個作業類型 QR 碼`);
        }
        
        // 產生完整作業繳交 QR 碼
        function generateFullQR() {
            console.log('開始產生完整作業繳交 QR 碼...');

            if (typeof QRCode === 'undefined') {
                console.error('QRCode 庫未載入');
                alert('QR 碼庫尚未載入完成，請稍後再試');
                return;
            }

            const studentId = document.getElementById('fullStudentId').value;
            const assignment = document.getElementById('fullAssignment').value;
            const container = document.getElementById('fullQR');

            if (!studentId || !assignment) {
                alert('請填入學生座號和作業名稱');
                return;
            }

            const qrData = `HW|${studentId}|${assignment}`;

            container.innerHTML = '<div class="qr-item" style="display: inline-block;"></div>';
            const qrItem = container.querySelector('.qr-item');

            const canvas = document.createElement('canvas');
            QRCode.toCanvas(canvas, qrData, { width: 200 }, function (error) {
                if (error) {
                    console.error('QR 碼產生錯誤:', error);
                    qrItem.innerHTML = `<div style="color: red;">產生失敗: ${qrData}</div>`;
                } else {
                    console.log('成功產生完整 QR 碼:', qrData);
                }
            });

            qrItem.appendChild(canvas);
            qrItem.innerHTML += `<div class="qr-text">${qrData}</div>`;

            console.log('完成產生完整作業繳交 QR 碼');
        }
        
        // 初始化 QR 碼產生器
        function initializeQRGenerator() {
            console.log('初始化 QR 碼產生器...');
            try {
                generateStudentQRs();
                generateAssignmentQRs();
                generateFullQR();
                console.log('QR 碼產生器初始化完成');
            } catch (error) {
                console.error('QR 碼產生器初始化失敗:', error);
                alert('QR 碼產生器初始化失敗，請重新整理頁面');
            }
        }

        // 頁面載入時的處理已移到上方的 load 事件監聽器中
    </script>
</body>
</html>
