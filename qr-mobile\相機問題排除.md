# 📱 手機相機無法開啟問題排除

## 🔍 常見問題與解決方案

### 1. **瀏覽器權限問題**

#### iPhone Safari
1. 開啟「設定」→「Safari」
2. 找到「相機」選項
3. 選擇「允許」或「詢問」
4. 重新整理網頁，點擊「允許」使用相機

#### Android Chrome
1. 開啟 Chrome 瀏覽器
2. 點擊右上角「⋮」→「設定」
3. 選擇「網站設定」→「相機」
4. 確保設定為「允許網站使用相機」
5. 或在網址列點擊「🔒」圖示，允許相機權限

### 2. **HTTPS 安全性問題**

現代瀏覽器基於安全考量，只允許 HTTPS 網站使用相機。

#### 解決方案 A：使用 HTTPS（推薦）
系統已自動嘗試啟用 HTTPS，如果看到憑證警告：
1. 點擊「進階」或「詳細資料」
2. 選擇「繼續前往網站」或「接受風險並繼續」
3. 網址會變成 `https://*************:8000`

#### 解決方案 B：瀏覽器設定（臨時）
如果無法使用 HTTPS，可以臨時允許 HTTP 使用相機：

**Chrome（電腦版測試用）**
1. 在網址列輸入：`chrome://flags/#unsafely-treat-insecure-origin-as-secure`
2. 在「Insecure origins treated as secure」中加入：`http://*************:8000`
3. 重啟瀏覽器

### 3. **手機瀏覽器相容性**

#### 推薦瀏覽器
- ✅ **iPhone Safari**（iOS 11+）
- ✅ **Android Chrome**（版本 53+）
- ✅ **Samsung Internet**
- ⚠️ **微信內建瀏覽器**（可能有限制）
- ⚠️ **LINE 內建瀏覽器**（可能有限制）

#### 如果在社群軟體內開啟
1. 點擊右上角「⋮」或「...」
2. 選擇「在瀏覽器中開啟」
3. 選擇 Safari 或 Chrome

### 4. **網路連線問題**

#### 檢查清單
- [ ] 手機和電腦在同一個 Wi-Fi 網路
- [ ] 電腦防火牆沒有阻擋 8000 埠
- [ ] 網址正確：`http://*************:8000`
- [ ] 系統正在運行（終端機顯示 "Running on..."）

### 5. **替代方案**

#### 方案 A：手動輸入
如果相機無法使用，可以使用手動輸入功能：
1. 在「手動輸入」欄位輸入：`HW|1-05|數學習作P25`
2. 點擊「送出」按鈕

#### 方案 B：使用其他 QR 掃描 APP
1. 使用手機內建的 QR 掃描功能
2. 掃描後複製文字
3. 貼到「手動輸入」欄位

### 6. **測試步驟**

#### 基本測試
1. 開啟手機瀏覽器
2. 輸入網址：`http://*************:8000`
3. 看到掃描頁面
4. 點擊「開始掃描」
5. 瀏覽器應該會詢問相機權限

#### 進階測試
1. 在電腦瀏覽器開啟相同網址測試
2. 檢查瀏覽器開發者工具的錯誤訊息
3. 嘗試不同的手機瀏覽器

### 7. **錯誤訊息對照**

| 錯誤訊息 | 原因 | 解決方案 |
|---------|------|----------|
| "Camera not found" | 沒有相機權限 | 允許瀏覽器使用相機 |
| "NotAllowedError" | 用戶拒絕權限 | 重新整理頁面，點擊允許 |
| "NotSecureError" | HTTP 安全性限制 | 使用 HTTPS 或設定瀏覽器 |
| "NotFoundError" | 找不到相機 | 檢查相機是否被其他 APP 佔用 |

### 8. **聯絡支援**

如果以上方法都無法解決，請提供以下資訊：
- 手機型號和作業系統版本
- 瀏覽器名稱和版本
- 具體錯誤訊息
- 網址是否正確顯示頁面

### 💡 小技巧

1. **第一次使用**建議先在電腦瀏覽器測試
2. **確保網路穩定**，避免在訊號不好的地方使用
3. **保持相機鏡頭清潔**，提高掃描成功率
4. **QR 碼大小**建議至少 2x2 公分
5. **光線充足**的環境下掃描效果更好

記住：如果相機真的無法使用，手動輸入功能一樣可以完成作業記錄！
