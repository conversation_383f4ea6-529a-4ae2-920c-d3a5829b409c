#!/bin/bash

# 設定顏色
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo
echo "========================================"
echo "   📚 小學作業掃描系統 一鍵啟動"
echo "========================================"
echo

# 檢查是否在正確的資料夾
if [ ! -f "app.py" ]; then
    echo -e "${RED}❌ 錯誤：找不到 app.py 檔案${NC}"
    echo "請確認您在 qr-mobile 資料夾中執行此腳本"
    read -p "按 Enter 鍵退出..."
    exit 1
fi

# 檢查 Python 是否安裝
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo -e "${RED}❌ 錯誤：找不到 Python${NC}"
        echo "請先安裝 Python 3.7 或更新版本"
        echo "macOS: brew install python3"
        echo "Ubuntu: sudo apt install python3 python3-pip python3-venv"
        read -p "按 Enter 鍵退出..."
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

echo -e "${GREEN}✅ Python 已安裝${NC}"
$PYTHON_CMD --version

# 檢查虛擬環境是否存在
if [ ! -d ".venv" ]; then
    echo
    echo -e "${BLUE}🔧 建立虛擬環境中...${NC}"
    $PYTHON_CMD -m venv .venv
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ 建立虛擬環境失敗${NC}"
        read -p "按 Enter 鍵退出..."
        exit 1
    fi
    echo -e "${GREEN}✅ 虛擬環境建立完成${NC}"
fi

# 啟動虛擬環境
echo
echo -e "${BLUE}🔧 啟動虛擬環境...${NC}"
source .venv/bin/activate
if [ $? -ne 0 ]; then
    echo -e "${RED}❌ 啟動虛擬環境失敗${NC}"
    read -p "按 Enter 鍵退出..."
    exit 1
fi

# 檢查是否需要安裝套件
if ! pip show flask &> /dev/null; then
    echo
    echo -e "${BLUE}📦 安裝必要套件中...${NC}"
    pip install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ 安裝套件失敗${NC}"
        read -p "按 Enter 鍵退出..."
        exit 1
    fi
    echo -e "${GREEN}✅ 套件安裝完成${NC}"
fi

# 顯示網路資訊
echo
echo "========================================"
echo "   🌐 網路連線資訊"
echo "========================================"

# 取得 IP 位址
if command -v ifconfig &> /dev/null; then
    IP=$(ifconfig | grep -Eo 'inet (addr:)?([0-9]*\.){3}[0-9]*' | grep -Eo '([0-9]*\.){3}[0-9]*' | grep -v '127.0.0.1' | head -1)
elif command -v ip &> /dev/null; then
    IP=$(ip route get 1 | awk '{print $7}' | head -1)
else
    IP="請手動查詢您的 IP 位址"
fi

if [ ! -z "$IP" ] && [ "$IP" != "請手動查詢您的 IP 位址" ]; then
    echo -e "${GREEN}電腦 IP 位址：$IP${NC}"
    echo -e "${YELLOW}手機請開啟：http://$IP:8000${NC}"
else
    echo -e "${YELLOW}請手動查詢您的電腦 IP 位址${NC}"
    echo "然後在手機瀏覽器開啟：http://[您的IP]:8000"
fi

echo
echo "========================================"
echo "   🚀 啟動系統"
echo "========================================"
echo
echo -e "${BLUE}💡 使用說明：${NC}"
echo "1. 確保手機和電腦連接同一個 Wi-Fi"
echo "2. 手機瀏覽器開啟上方網址"
echo "3. 允許相機權限"
echo "4. 開始掃描 QR 碼！"
echo
echo -e "${BLUE}📱 QR 碼格式：${NC}"
echo "   作業繳交：HW|學號|作業名稱"
echo "   範例：HW|1-05|數學習作P25"
echo
echo -e "${RED}🛑 要停止系統請按 Ctrl+C${NC}"
echo

# 啟動 Flask 應用
python app.py

echo
echo "系統已停止運行"
read -p "按 Enter 鍵退出..."
