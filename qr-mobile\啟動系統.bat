@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion
title 小學作業掃描系統

echo.
echo ========================================
echo    📚 小學作業掃描系統 一鍵啟動
echo ========================================
echo.
echo 🎯 這個系統可以幫助老師：
echo    ✅ 即時掌握學生作業繳交狀況
echo    ✅ 自動記錄，避免遺漏
echo    ✅ 手機掃描，操作簡單
echo    ✅ 查看未繳交清單
echo.

:: 檢查是否在正確的資料夾
if not exist "app.py" (
    echo ❌ 錯誤：找不到 app.py 檔案
    echo 請確認您在 qr-mobile 資料夾中執行此腳本
    pause
    exit /b 1
)

:: 檢查 Python 是否安裝
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 錯誤：找不到 Python
    echo 請先安裝 Python 3.7 或更新版本
    echo 下載網址：https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python 已安裝
python --version

:: 檢查虛擬環境是否存在
if not exist ".venv" (
    echo.
    echo 🔧 建立虛擬環境中...
    python -m venv .venv
    if errorlevel 1 (
        echo ❌ 建立虛擬環境失敗
        pause
        exit /b 1
    )
    echo ✅ 虛擬環境建立完成
)

:: 啟動虛擬環境
echo.
echo 🔧 啟動虛擬環境...
call .venv\Scripts\activate.bat
if errorlevel 1 (
    echo ❌ 啟動虛擬環境失敗
    pause
    exit /b 1
)

:: 檢查是否需要安裝套件
pip show flask >nul 2>&1
if errorlevel 1 (
    echo.
    echo 📦 安裝必要套件中...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ 安裝套件失敗
        pause
        exit /b 1
    )
    echo ✅ 套件安裝完成
)

:: 顯示網路資訊
echo.
echo ========================================
echo    🌐 網路連線資訊
echo ========================================
for /f "tokens=2 delims=:" %%i in ('ipconfig ^| findstr /i "IPv4"') do (
    set ip=%%i
    set ip=!ip: =!
    if not "!ip!"=="" (
        echo 電腦 IP 位址：!ip!
        echo 手機請開啟：http://!ip!:5000
        goto :found_ip
    )
)
:found_ip

echo.
echo ========================================
echo    🚀 啟動系統
echo ========================================
echo.
echo 💡 使用說明：
echo 1. 確保手機和電腦連接同一個 Wi-Fi
echo 2. 手機瀏覽器開啟上方網址
echo 3. 允許相機權限
echo 4. 開始掃描 QR 碼！
echo.
echo 📱 QR 碼格式：
echo    作業繳交：HW^|學號^|作業名稱
echo    範例：HW^|1-05^|數學習作P25
echo.
echo 🛑 要停止系統請按 Ctrl+C
echo.

:: 啟動 Flask 應用
python app.py

echo.
echo 系統已停止運行
pause
